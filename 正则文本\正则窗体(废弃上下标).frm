Option Explicit

' 窗体级变量声明
Private sCount As Long
Private TextColor As Color

' 用于存储预览列表中对应的文本形状信息
Private Type PreviewTextInfo
    ShapeRef As Shape     ' 对应的形状引用
    PreviewIndex As Long  ' 对应预览列表索引
End Type
Private PreviewTextItems() As PreviewTextInfo
Private PreviewTextCount As Long

' 用于存储已处理过的PowerClip容器ID
Private processedContainers As Collection

' 窗体初始化
Private Sub UserForm_Initialize()
    Me.Caption = macroName
    
    ' 初始化预览文本项数组
    ReDim PreviewTextItems(0 To 99)
    PreviewTextCount = 0
    
    ' 初始化颜色对象
    Set TextColor = New Color
    TextColor.CMYKAssign 0, 100, 100, 0
    
    ' 设置颜色按钮的背景色
    If Not TextColor Is Nothing Then
        Dim tempColor As New Color
        tempColor.CopyAssign TextColor
        tempColor.ConvertToRGB
        Color.BackColor = RGB(tempColor.RGBRed, tempColor.RGBGreen, tempColor.RGBBlue)
    End If
    
    ' 初始化查找范围下拉框
    fanwen.AddItem "当前文档"
    fanwen.AddItem "当前页面"
    fanwen.AddItem "当前选择范围"
    
    ' 根据当前选择状态设置默认范围
    If ActiveSelectionRange.Count > 0 Then
        fanwen.Text = "当前选择范围"
    Else
        fanwen.Text = "当前文档"
    End If
    
    ' 初始化字体列表
    LoadFontList
    
    ' 初始化字号默认值
    zihaodaxiao.Text = "24"
    zihaodaxiaoanjian.Value = 24
    zihaodaxiaoanjian.Min = 1
    zihaodaxiaoanjian.Max = 3000
    
    ' 初始化格式数组
    FindFormat(0) = "0"
End Sub

' 加载字体列表到下拉框
Private Sub LoadFontList()
    Dim nDefaultFont As Integer, nCount As Integer
    Dim strDefaultFont As String
    Dim v As Variant
    
    strDefaultFont = "方正大黑_GBK"
    nDefaultFont = 0
    nCount = 0
    
    zitisousuo.Clear
    
    On Error Resume Next
    
    ' 遍历FontList，过滤掉@开头的字体
    For Each v In FontList
        If Not Left(v, 1) = "@" Then
            zitisousuo.AddItem v
            If v = strDefaultFont Then nDefaultFont = nCount
            nCount = nCount + 1
        End If
    Next v
    
    ' 设置默认选择
    If zitisousuo.ListCount > 0 Then
        zitisousuo.ListIndex = nDefaultFont
    End If
    
    On Error GoTo 0
End Sub

' 颜色选择按钮点击事件
Private Sub Color_Click()
    Dim c As New Color
    Dim b As Boolean
    With c
        b = .UserAssignEx
        If b Then
            TextColor.CopyAssign c
            
            Dim dispColor As New Color
            dispColor.CopyAssign c
            If dispColor.Type <> cdrColorRGB Then dispColor.ConvertToRGB
            
            Color.BackColor = RGB(dispColor.RGBRed, dispColor.RGBGreen, dispColor.RGBBlue)
        End If
    End With
End Sub

' 替换按钮点击事件
Private Sub tihuan_Click()
    ' 执行替换操作
    Dim findText As String
    Dim replaceText As String
    Dim shapes As ShapeRange
    Dim replaceCount As Long
    Dim formatOnlyMode As Boolean
    
    findText = chazhaowenben.Text
    replaceText = tihuanwenben.Text
    
    ' 检查替换文本中是否有\r\n，并转换为实际换行符
    replaceText = Replace(replaceText, "\r\n", vbCrLf)
    
    If Len(findText) = 0 Then
        MsgBox "请输入要查找的文本", vbInformation
        Exit Sub
    End If
    
    ' 检查替换文本为空且有格式选项被勾选的情况
    formatOnlyMode = False
    If Len(replaceText) = 0 Then
        ' 检查是否有任意格式选项被勾选
        If ziti.Value = True Or zihao.Value = True Or tihuanyanse.Value = True Or _
           Bold.Value = True Or xieti.Value = True Or xiahuaxian.Value = True Or quxiao.Value = True Then
            formatOnlyMode = True
        End If
    End If
    
    ' 获取要查找的形状范围
    Set shapes = myFindShapes()
    If shapes Is Nothing Then
        MsgBox "没有找到可查找的文本对象", vbInformation
        Exit Sub
    End If
    
    ' 开始命令组，方便用户一次性撤销所有替换操作
    On Error GoTo ErrorHandler

    ' 根据处理范围动态设置命令组名称
    Dim commandGroupName As String
    commandGroupName = "文本替换"

    If rongqi.Value = True And qunzu.Value = True Then
        commandGroupName = "文本替换(包含容器和群组)"
    ElseIf rongqi.Value = True Then
        commandGroupName = "文本替换(包含容器)"
    ElseIf qunzu.Value = True Then
        commandGroupName = "文本替换(包含群组)"
    End If

    ActiveDocument.BeginCommandGroup commandGroupName

    ' 执行替换操作
    replaceCount = myReplaceText(shapes, findText, replaceText, False, formatOnlyMode)

    ' 结束命令组
    ActiveDocument.EndCommandGroup
    On Error GoTo 0
    
    ' 更新预览列表
    UpdatePreview
    
    ' 根据模式显示不同的完成消息
    If formatOnlyMode Then
        MsgBox "格式应用完成！共处理了 " & replaceCount & " 个匹配项", vbInformation
    Else
        MsgBox "替换完成！共替换了 " & replaceCount & " 个匹配项", vbInformation
    End If
    Exit Sub

ErrorHandler:
    ' 如果出现错误，确保结束命令组
    On Error Resume Next
    ActiveDocument.EndCommandGroup
    On Error GoTo 0

    ' 显示错误信息
    MsgBox "替换过程中出现错误: " & Err.Description, vbExclamation
    Err.Clear
End Sub

' 查找按钮点击事件
Private Sub chazhao_Click()
    Dim findText As String
    Dim shapes As ShapeRange
    Dim foundCount As Long
    
    findText = chazhaowenben.Text
    If Len(findText) = 0 Then
        MsgBox "请输入要查找的文本", vbInformation
        Exit Sub
    End If
    
    ' 获取要查找的形状范围
    Set shapes = myFindShapes()
    If shapes Is Nothing Then
        MsgBox "没有找到可查找的文本对象", vbInformation
        Exit Sub
    End If
    
    ' 执行查找并统计
    foundCount = myReplaceText(shapes, findText, "", True) ' 只查找不替换
    
    ' 更新预览列表
    UpdatePreview
    
    MsgBox "找到 " & foundCount & " 个匹配项", vbInformation
End Sub

' 使用鼠标事件处理双击
Private Sub yulan_MouseUp(ByVal Button As Integer, ByVal Shift As Integer, ByVal X As Single, ByVal Y As Single)
    Static lastClickTime As Double
    Static lastClickIndex As Long
    
    ' 获取当前选择的列表项索引
    Dim selectedIndex As Long
    selectedIndex = yulan.ListIndex
    
    ' 只处理左键点击
    If Button = 1 Then
        ' 检查是否是双击（在同一项上的两次快速点击）
        If (Timer - lastClickTime) < 0.5 And selectedIndex = lastClickIndex Then
            ' 执行双击操作
            JumpToTextShape selectedIndex
        End If
        
        ' 记录本次点击的时间和索引，为下次判断双击做准备
        lastClickTime = Timer
        lastClickIndex = selectedIndex
    End If
End Sub

' 将定位到形状的代码移到单独的函数中
Private Sub JumpToTextShape(ByVal selectedIndex As Long)
    ' 检查索引有效性并找到对应的形状
    If selectedIndex >= 0 And selectedIndex < PreviewTextCount Then
        Dim targetShape As Shape
        Set targetShape = PreviewTextItems(selectedIndex).ShapeRef
        
        ' 检查形状是否存在
        On Error Resume Next
        Dim shapeExists As Boolean
        shapeExists = False
        
        ' 尝试访问形状的属性来检查它是否存在
        If Not targetShape Is Nothing Then
            Dim tempVar
            tempVar = targetShape.Name
            If Err.Number = 0 Then
                shapeExists = True
            End If
        End If
        On Error GoTo 0
        
        ' 如果形状不存在，显示提示消息
        If Not shapeExists Then
            MsgBox "你选择的对象已被删除，导致无法定位。", vbExclamation
            Exit Sub
        End If
        
        ' 如果找到有效的形状引用，则将视图定位到该形状
        If Not targetShape Is Nothing Then
            ' 激活形状所在的页面
            targetShape.Page.Activate
            
            ' 确保完全清除当前选择范围
            ActiveDocument.ClearSelection
            
            ' 将形状添加到选择
            targetShape.AddToSelection
            
            ' 使用ToFitSelection方法让视图跟随到选中对象
            ActiveDocument.ActiveWindow.ActiveView.ToFitSelection
            
            ' 激活窗口，方便用户使用快捷键
            ActiveWindow.Activate
        End If
    End If
End Sub

' 字号勾选框点击事件
Private Sub zihao_Click()
    zihaodaxiao.Enabled = zihao.Value
    zihaodaxiaoanjian.Enabled = zihao.Value
End Sub

' 字体勾选框点击事件
Private Sub ziti_Click()
    zitisousuo.Enabled = ziti.Value
End Sub
' 字号大小输入框变化事件
Private Sub zihaodaxiao_Change()
    ' 同步TextBox和SpinButton的值
    If IsNumeric(zihaodaxiao.Text) Then
        Dim newSize As Double
        newSize = CDbl(zihaodaxiao.Text)
        If newSize >= 1 And newSize <= 3000 Then
            zihaodaxiaoanjian.Value = newSize
        End If
    End If
End Sub

' 字号调整按钮鼠标按下事件
Private Sub zihaodaxiaoanjian_MouseDown(ByVal Button As Integer, ByVal Shift As Integer, ByVal X As Single, ByVal Y As Single)
    Dim currentSize As Double
    Dim increment As Double
    
    ' 获取当前字号
    If IsNumeric(zihaodaxiao.Text) Then
        currentSize = CDbl(zihaodaxiao.Text)
    Else
        currentSize = 24 ' 默认字号
    End If
    
    ' 根据鼠标按键确定增减量
    ' 检测右键是否按下，如果按下了，左键点击递增量为10
    If Button = 1 Then ' 左键点击
        If (Shift And 2) = 2 Then ' 检测右键是否按下(Shift值2表示右键)
            increment = 10
        Else
            increment = 1
        End If
    Else
        increment = 1 ' 默认增减量
    End If
    
    ' 判断点击位置确定增减方向
    If Y < zihaodaxiaoanjian.Height / 2 Then
        currentSize = currentSize + increment
    Else
        currentSize = currentSize - increment
    End If
    
    ' 限制字号范围
    If currentSize < 1 Then currentSize = 1
    If currentSize > 3000 Then currentSize = 3000
    
    ' 更新字号值
    zihaodaxiao.Text = CStr(currentSize)
End Sub

' 字号调整按钮变化事件
Private Sub zihaodaxiaoanjian_Change()
    ' 同步SpinButton和TextBox的值
    If IsNumeric(zihaodaxiaoanjian.Value) Then
        zihaodaxiao.Text = CStr(zihaodaxiaoanjian.Value)
    End If
End Sub

' 替换颜色勾选框点击事件
Private Sub tihuanyanse_Click()
    Color.Enabled = tihuanyanse.Value
End Sub

' 获取要查找的形状范围
Private Function myFindShapes() As ShapeRange
    Dim sr As New ShapeRange
    Dim basicShapes As ShapeRange

    ' 初始化已处理容器列表
    Set processedContainers = New Collection

    Select Case fanwen.Text
        Case "当前文档"
            Dim p As Page
            Dim startPage As Page
            Set startPage = ActivePage
            For Each p In ActiveDocument.Pages
                p.Activate
                Set basicShapes = ActivePage.FindShapes(, cdrTextShape)
                sr.AddRange basicShapes

                ' 处理容器和群组
                If rongqi.Value = True Or qunzu.Value = True Then
                    ProcessAdditionalShapes ActivePage.Shapes, sr
                End If
            Next
            startPage.Activate
        Case "当前页面"
            Set basicShapes = ActivePage.FindShapes(, cdrTextShape)
            sr.AddRange basicShapes

            ' 处理容器和群组
            If rongqi.Value = True Or qunzu.Value = True Then
                ProcessAdditionalShapes ActivePage.Shapes, sr
            End If
        Case "当前选择范围"
            sr.AddRange ActiveSelectionRange

            ' 处理选择范围中的容器和群组
            If rongqi.Value = True Or qunzu.Value = True Then
                ProcessAdditionalShapesFromRange ActiveSelectionRange, sr
            End If
    End Select

    Set myFindShapes = sr
End Function

' 文本替换核心函数
Private Function myReplaceText(sr As ShapeRange, sFind As String, sRep As String, Optional findOnly As Boolean = False, Optional formatOnlyMode As Boolean = False) As Long
    Dim reg As Object
    Dim replaceCount As Long
    Dim i As Long
    
    replaceCount = 0
    
    ' 处理替换文本中的\r\n为实际换行符
    If InStr(sRep, "\r\n") > 0 Then
        sRep = Replace(sRep, "\r\n", vbCrLf)
    End If
    
    If sr Is Nothing Or sr.Count < 1 Then
        myReplaceText = 0
        Exit Function
    End If
    
    If Trim(sFind) = "" Then
        myReplaceText = 0
        Exit Function
    End If
    
    ' 获取是否是正则表达式替换
    Dim isRegexPattern As Boolean
    isRegexPattern = False
    For i = 1 To Len(sFind)
        Dim c As String
        c = Mid(sFind, i, 1)
        If c = "." Or c = "*" Or c = "?" Or c = "+" Or c = "^" Or c = "$" Or c = "\" Or c = "[" Or c = "]" Or c = "(" Or c = ")" Then
            isRegexPattern = True
            Exit For
        End If
    Next i
    
    ' 创建正则表达式对象
    Set reg = CreateObject("VBScript.RegExp")
    reg.Pattern = sFind
    reg.IgnoreCase = False
    reg.MultiLine = True
    reg.Global = True
    
    Dim sh As Shape
    
    ' 遍历所有文本形状进行查找和替换
    For Each sh In sr
        If sh.Type = cdrTextShape Then
            Dim txt As TextRange, matches As Object, fi As Long
            Set txt = sh.Text.Story
            
            Set matches = reg.Execute(txt.Text)
            
            For i = matches.Count - 1 To 0 Step -1
                With matches(i)
                    If .FirstIndex = 0 Then fi = 0 Else fi = .FirstIndex
                    Dim tr As TextRange
                    Set tr = txt.Range(fi, fi + .Length)
                    
                    ' 查找模式：只统计匹配项，不做任何修改
                    If findOnly Then
                        replaceCount = replaceCount + 1
                    Else
                        ' 根据模式决定是否替换文本内容
                        If Not formatOnlyMode Then
                            ' 正常替换模式：执行文本替换，再应用格式
                            
                            ' 使用正则表达式对象的Replace方法替换文本
                            Dim replacedText As String
                            
                            If isRegexPattern Then
                                replacedText = reg.Replace(.Value, sRep)
                            Else
                                replacedText = sRep
                            End If
                            
                            ' 替换文本内容
                            tr.Text = replacedText
                        End If
                        
                        ' 无论是否为只格式模式，都应用格式
                        ApplyChangeFormatIfEnabled tr
                        
                        replaceCount = replaceCount + 1
                    End If
                    
                    Set tr = Nothing
                End With
            Next i
        End If
    Next sh
    
    myReplaceText = replaceCount
End Function

' 检查勾选框状态并应用替换格式
Private Sub ApplyChangeFormatIfEnabled(tr As TextRange)
    ' 检查字体勾选框并应用字体
    If ziti.Value = True And Trim(zitisousuo.Text) <> "" Then
        On Error Resume Next
        tr.Font = zitisousuo.Text
        On Error GoTo 0
    End If
    
    ' 检查字号勾选框并应用字号
    If zihao.Value = True And Trim(zihaodaxiao.Text) <> "" Then
        On Error Resume Next
        tr.Size = CDbl(zihaodaxiao.Text)
        On Error GoTo 0
    End If
    
    ' 检查颜色勾选框并应用颜色
    If tihuanyanse.Value = True Then
        On Error Resume Next
        If Not TextColor Is Nothing Then
            Dim colorToApply As New Color
            colorToApply.CopyAssign TextColor
            
            tr.Fill.UniformColor.CopyAssign colorToApply
        End If
        On Error GoTo 0
    End If
    
    ' 检查粗体勾选框并应用粗体
    If Bold.Value = True Then
        On Error Resume Next
        tr.Bold = True
        On Error GoTo 0
    End If
    
    ' 检查斜体勾选框并应用斜体
    If xieti.Value = True Then
        On Error Resume Next
        tr.Italic = True
        On Error GoTo 0
    End If
    
    ' 检查下划线勾选框并应用下划线
    If xiahuaxian.Value = True Then
        On Error Resume Next
        tr.Underline = cdrSingleThinFontLine
        On Error GoTo 0
    End If
    
    ' 检查取消设置勾选框并取消对应格式
    If quxiao.Value = True Then
        On Error Resume Next
        
        ' 如果勾选了粗体，取消粗体格式
        If Bold.Value = True Then
            tr.Bold = False
        End If
        
        ' 如果勾选了斜体，取消斜体格式
        If xieti.Value = True Then
            tr.Italic = False
        End If
        
        ' 如果勾选了下划线，取消下划线格式
        If xiahuaxian.Value = True Then
            tr.Underline = cdrNoFontLine
        End If
        
        On Error GoTo 0
    End If
End Sub

' 更新预览列表
Private Sub UpdatePreview()
    If chazhaowenben.Text = "" Then Exit Sub
    
    yulan.Clear
    
    ' 清空并重新初始化预览文本数组
    ReDim PreviewTextItems(0 To 99)
    PreviewTextCount = 0
    
    Dim sr As ShapeRange, reg As Object
    Set sr = myFindShapes
    
    If sr.Count < 1 Then Exit Sub
    
    Set reg = CreateObject("VBScript.RegExp")
    reg.Pattern = chazhaowenben.Text
    reg.IgnoreCase = False
    reg.MultiLine = True
    reg.Global = True
    
    Dim sh As Shape, previewCount As Long
    previewCount = 0
    
    For Each sh In sr
        If sh.Type = cdrTextShape And previewCount < 100 Then ' 限制预览数量为100
            Dim txt As TextRange, matches As Object
            Set txt = sh.Text.Story
            Set matches = reg.Execute(txt)
            
            If matches.Count > 0 Then
                Dim previewText As String
                previewText = Left(txt.Text, 100) ' 显示前100个字符
                If Len(txt.Text) > 100 Then previewText = previewText & "..."
                yulan.AddItem previewText
                
                ' 记录当前预览项与形状的对应关系
                Set PreviewTextItems(previewCount).ShapeRef = sh
                PreviewTextItems(previewCount).PreviewIndex = previewCount
                
                previewCount = previewCount + 1
                PreviewTextCount = previewCount
            End If
        End If
    Next sh
End Sub

' 处理额外的形状（容器和群组）
Private Sub ProcessAdditionalShapes(shapes As Shapes, ByRef sr As ShapeRange)
    Dim sh As Shape

    For Each sh In shapes
        ' 处理容器
        If rongqi.Value = True And HasPowerClip(sh) Then
            ProcessPowerClipForText sh, sr
        End If

        ' 处理群组
        If qunzu.Value = True And sh.Type = cdrGroupShape Then
            ProcessGroupForText sh, sr
        End If
    Next sh
End Sub

' 处理选择范围中的额外形状（容器和群组）
Private Sub ProcessAdditionalShapesFromRange(shapeRange As ShapeRange, ByRef sr As ShapeRange)
    Dim sh As Shape

    For Each sh In shapeRange
        ' 处理容器
        If rongqi.Value = True And HasPowerClip(sh) Then
            ProcessPowerClipForText sh, sr
        End If

        ' 处理群组
        If qunzu.Value = True And sh.Type = cdrGroupShape Then
            ProcessGroupForText sh, sr
        End If
    Next sh
End Sub

' 检查对象是否有PowerClip
Private Function HasPowerClip(ByVal obj As Shape) As Boolean
    On Error Resume Next
    HasPowerClip = False

    Dim pwc As PowerClip
    Set pwc = obj.PowerClip

    If Err.Number = 0 And Not pwc Is Nothing Then
        HasPowerClip = True
    End If

    Err.Clear
End Function

' 检查容器是否已处理过
Private Function IsContainerProcessed(ByVal id As Long) As Boolean
    On Error Resume Next
    If processedContainers Is Nothing Then
        IsContainerProcessed = False
        Exit Function
    End If

    Dim temp As Variant
    temp = processedContainers(CStr(id))
    IsContainerProcessed = (Err.Number = 0)
    Err.Clear
End Function

' 标记对象为已处理
Private Sub MarkAsProcessed(ByVal id As Long)
    On Error Resume Next
    If Not processedContainers Is Nothing Then
        processedContainers.Add True, CStr(id)
    End If
    Err.Clear
End Sub

' 处理PowerClip容器中的文本
Private Sub ProcessPowerClipForText(ByVal container As Shape, ByRef sr As ShapeRange)
    On Error Resume Next

    ' 检查是否已处理过此容器
    If IsContainerProcessed(container.StaticID) Then
        Exit Sub
    End If

    ' 标记此容器为已处理
    MarkAsProcessed container.StaticID

    ' 进入PowerClip编辑模式
    container.PowerClip.EnterEditMode
    If Err.Number <> 0 Then
        Err.Clear
        Exit Sub
    End If

    ' 获取PowerClip内部的所有形状
    Dim innerShapes As Shapes
    Set innerShapes = ActiveLayer.Shapes

    ' 如果ActiveLayer.Shapes为空，尝试使用ActivePage.Shapes
    If innerShapes.Count = 0 Then
        Set innerShapes = ActivePage.Shapes
    End If

    ' 递归处理PowerClip内部的所有对象
    Dim s As Shape
    For Each s In innerShapes
        ProcessShapeForText s, sr
    Next s

    ' 离开PowerClip编辑模式
    container.PowerClip.LeaveEditMode
    Err.Clear
End Sub

' 处理群组中的文本
Private Sub ProcessGroupForText(ByVal groupObj As Shape, ByRef sr As ShapeRange)
    On Error Resume Next

    Dim childObj As Shape
    For Each childObj In groupObj.Shapes
        ProcessShapeForText childObj, sr
    Next childObj

    Err.Clear
End Sub

' 通用形状处理函数，递归处理各种类型的形状
Private Sub ProcessShapeForText(ByVal shape As Shape, ByRef sr As ShapeRange)
    On Error Resume Next

    ' 如果是文本对象，直接添加
    If shape.Type = cdrTextShape Then
        sr.Add shape
        Exit Sub
    End If

    ' 如果是群组且群组被勾选，递归处理
    If shape.Type = cdrGroupShape And qunzu.Value = True Then
        ProcessGroupForText shape, sr
        Exit Sub
    End If

    ' 如果是PowerClip容器且容器被勾选，递归处理
    If rongqi.Value = True And HasPowerClip(shape) Then
        ProcessPowerClipForText shape, sr
        Exit Sub
    End If

    ' 处理其他包含子对象的特殊组
    If shape.Type = cdrBlendGroupShape Or shape.Type = cdrExtrudeGroupShape Or _
       shape.Type = cdrContourGroupShape Or shape.Type = cdrBevelGroupShape Or _
       shape.Type = cdrDropShadowGroupShape Or shape.Type = cdrArtisticMediaGroupShape Or _
       shape.Type = cdrCustomEffectGroupShape Or shape.Type = cdrSymbolShape Then

        Dim childShape As Shape
        For Each childShape In shape.Shapes
            ProcessShapeForText childShape, sr
        Next childShape
    End If

    Err.Clear
End Sub