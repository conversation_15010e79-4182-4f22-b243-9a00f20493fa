Private Const TOOLNAME As String = "JinGuangHe" '声明两个常量 用于储存
Private Const SECTION As String = "JGHDate" '声明两个常量 用于储存

' 用于存储已处理过的PowerClip容器ID
Private processedContainers As Collection

Private Sub CommandButton23_Click()
On Error Resume Next
    ActiveDocument.Unit = cdrMillimeter
    Application.Optimization = True
    ActiveDocument.BeginCommandGroup
    ActiveDocument.ReferencePoint = cdrCenter
    If ActiveDocument Is Nothing Then Exit Sub
    

    Dim frontObjects() As Long
    Dim backObjects() As Long
    Dim i As Integer
    Dim filePath As String
    Dim obj As Shape
    Dim angle As Double
    Dim overallWidth As Double
    Dim mirrorDirection As Boolean
    Dim line As String
    Dim data() As String
    

    filePath = Application.GMSManager.GMSPath & "object_ids.txt"
    

    Open filePath For Input As #1
    i = 1
    Do While Not EOF(1)
        Line Input #1, line
        data = Split(line, ",")
        ReDim Preserve frontObjects(i)
        ReDim Preserve backObjects(i)
        frontObjects(i) = CLng(data(0))
        backObjects(i) = CLng(data(1))
        i = i + 1
    Loop
    Close #1
    
    
    
    ' 设置镜像方向（True = 水平, False = 垂直）
    mirrorDirection = OptionButton1.Value
    
    ' 计算正面对象的旋转角度并调整背面对象
    For i = 1 To UBound(frontObjects)
        ' 获取正面对象
        
        Set obj = ActivePage.Shapes.FindShape(, , frontObjects(i))
        angle = obj.RotationAngle ' 获取旋转角度
        
        Dim objb As Shape
        ' 获取背面对象并镜像和旋转
        Set objb = ActivePage.Shapes.FindShape(, , backObjects(i))
        
        ' 执行水平或垂直镜像
        If mirrorDirection Then
            objb.Flip cdrFlipHorizontal
        Else
            objb.Flip cdrFlipVertical
        End If
        
        objb.Rotate angle
        objb.LeftX = obj.LeftX
        objb.BottomY = obj.BottomY
        
    Next i
    
    For i = 1 To UBound(backObjects)
        ActivePage.Shapes.FindShape(, , backObjects(i)).AddToSelection
    Next i
    
    Dim s As ShapeRange
    Set s = ActiveSelectionRange
    If mirrorDirection Then
        s.Flip cdrFlipHorizontal
    Else
        s.Flip cdrFlipVertical
    End If
    overallWidth = s.SizeWidth ' 获取选区的整体宽度
    Dim gaps As Double
    gaps = TextBox3.Value
    s.Move overallWidth + gaps, 0 ' 移动到右侧5厘米位置
    
    'MsgBox "所有对象已处理完成！"
    
    ActiveDocument.EndCommandGroup
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    Exit Sub
ErrorHandler:
    Application.Optimization = False
    On Error Resume Next
End Sub

' 检查对象是否有PowerClip
Private Function HasPowerClip(ByVal obj As Shape) As Boolean
    On Error Resume Next
    HasPowerClip = False
    
    Dim pwc As PowerClip
    Set pwc = obj.PowerClip
    
    If Err.Number = 0 And Not pwc Is Nothing Then
        HasPowerClip = True
    End If
    
    Err.Clear
End Function

' 检查容器是否已处理过
Private Function IsContainerProcessed(ByVal id As Long) As Boolean
    On Error Resume Next
    If processedContainers Is Nothing Then
        IsContainerProcessed = False
        Exit Function
    End If
    
    Dim temp As Variant
    temp = processedContainers(CStr(id))
    IsContainerProcessed = (Err.Number = 0)
    Err.Clear
End Function

' 标记对象为已处理
Private Sub MarkAsProcessed(ByVal id As Long)
    On Error Resume Next
    If Not processedContainers Is Nothing Then
        processedContainers.Add True, CStr(id)
    End If
    Err.Clear
End Sub

' 处理PowerClip容器
Private Sub ProcessPowerClipContainer(ByVal container As Shape)
    On Error Resume Next
    
    ' 检查是否已处理过此容器
    If IsContainerProcessed(container.StaticID) Then
        Exit Sub
    End If
    
    ' 标记此容器为已处理
    MarkAsProcessed container.StaticID
    
    ' 进入PowerClip编辑模式
    container.PowerClip.EnterEditMode
    If Err.Number <> 0 Then
        Err.Clear
        Exit Sub
    End If
    
    ' 获取当前编辑视图中的所有对象
    Dim allShapes As Shapes
    Set allShapes = ActivePage.Shapes
    
    ' 创建一个临时选择范围
    Dim sr As ShapeRange
    Set sr = ActiveSelectionRange
    sr.DeselectAll
    
    ' 查找所有文本对象
    Dim textCount As Integer
    textCount = 0
    
    ' 首先尝试直接选择所有文本对象
    ActiveDocument.SelectableShapes.SelectShapesOfType cdrTextShape
    
    ' 检查是否选择了任何文本对象
    If ActiveSelectionRange.Count > 0 Then
        textCount = ActiveSelectionRange.Count
        ActiveSelectionRange.DeselectAll
    End If
    
    ' 然后处理可能嵌套在群组中的文本对象
    Dim s As Shape
    For Each s In allShapes
        If s.Type = cdrGroupShape Then
            HandleGroupWithUngroup s
        End If
    Next s
    
    ' 离开PowerClip编辑模式
    container.PowerClip.LeaveEditMode
End Sub

' 递归处理对象中的所有文本
Private Sub ProcessTextInObject(ByVal obj As Shape)
    On Error Resume Next
    
    ' 如果是文本对象，直接水平翻转恢复
    If obj.Type = cdrTextShape Then
        obj.Flip cdrFlipHorizontal
        Exit Sub
    End If
    
    ' 如果是 PowerClip，进入内部递归处理
    If HasPowerClip(obj) Then
        ProcessPowerClipText obj
        Exit Sub
    End If
    
    ' 如果是群组，递归处理其子对象
    If obj.Type = cdrGroupShape Then
        Dim childObj As Shape
        For Each childObj In obj.Shapes
            ProcessTextInObject childObj
        Next childObj
        Exit Sub
    End If
    
    ' 处理其他包含子对象的特殊组
    If obj.Type = cdrBlendGroupShape Or obj.Type = cdrExtrudeGroupShape Or _
       obj.Type = cdrContourGroupShape Or obj.Type = cdrBevelGroupShape Or _
       obj.Type = cdrDropShadowGroupShape Or obj.Type = cdrArtisticMediaGroupShape Or _
       obj.Type = cdrCustomEffectGroupShape Or obj.Type = cdrSymbolShape Then
        Dim childShape As Shape
        For Each childShape In obj.Shapes
            ProcessTextInObject childShape
        Next childShape
    End If
End Sub

' 处理 PowerClip 容器内所有对象
Private Sub ProcessPowerClipText(ByVal container As Shape)
    On Error Resume Next
    
    If IsContainerProcessed(container.StaticID) Then Exit Sub
    MarkAsProcessed container.StaticID
    
    container.PowerClip.EnterEditMode
    If Err.Number <> 0 Then Err.Clear: Exit Sub
    
    Dim innerShape As Shape
    For Each innerShape In ActiveLayer.Shapes
        ProcessTextInObject innerShape
    Next innerShape
    
    container.PowerClip.LeaveEditMode
End Sub

Private Sub CommandButton44_Click()
On Error GoTo ErrorHandler
    ActiveDocument.Unit = cdrMillimeter
    Application.Optimization = True
    ActiveDocument.BeginCommandGroup
    ActiveDocument.ReferencePoint = cdrCenter
    If ActiveDocument Is Nothing Then Exit Sub
    
    ' 初始化已处理容器列表
    Set processedContainers = New Collection
    
    ' 定义变量
    Dim backObjects() As Long
    Dim i As Integer
    Dim filePath As String
    Dim obj As Shape
    Dim line As String
    Dim data() As String
    
    ' 设置文件路径
    filePath = Application.GMSManager.GMSPath & "object_ids.txt"
    
    ' 检查文件是否存在
    If Dir(filePath) = "" Then
        MsgBox "请先标记正反面对象！"
        Exit Sub
    End If
    
    ' 从文件中读取反面对象ID
    Open filePath For Input As #1
    i = 1
    Do While Not EOF(1)
        Line Input #1, line
        data = Split(line, ",")
        ReDim Preserve backObjects(i)
        backObjects(i) = CLng(data(1)) ' 只取反面对象ID
        i = i + 1
    Loop
    Close #1
    
    ' 选择所有反面对象
    For i = 1 To UBound(backObjects)
        ActivePage.Shapes.FindShape(, , backObjects(i)).AddToSelection
    Next i
    
    ' 对所有反面对象进行整体水平镜像
    Dim s As ShapeRange
    Set s = ActiveSelectionRange
    s.Flip cdrFlipHorizontal
    
    ' 处理每个反面对象中的文本对象
    For i = 1 To UBound(backObjects)
        Set obj = ActivePage.Shapes.FindShape(, , backObjects(i))
        If Not obj Is Nothing Then
            ProcessTextInObject obj
        End If
    Next i
    
    ActiveDocument.EndCommandGroup
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    Exit Sub
ErrorHandler:
    Application.Optimization = False
    MsgBox "处理过程中出错: " & Err.Description, vbExclamation
    On Error Resume Next
End Sub

' 处理PowerClip容器内的群组
Private Sub HandleGroupWithUngroup(ByVal groupObj As Shape)
    On Error Resume Next
    
    Dim childObj As Shape
    For Each childObj In groupObj.Shapes
        If childObj.Type = cdrTextShape Then
            childObj.Flip cdrFlipHorizontal
        ElseIf childObj.Type = cdrGroupShape Then
            HandleGroupWithUngroup childObj
        End If
    Next childObj
    
    groupObj.CreateSelection
    ActiveDocument.BeginCommandGroup "解组处理"
    ActiveSelection.Ungroup
    
    ActiveDocument.EndCommandGroup
    ActiveSelectionRange.DeselectAll
End Sub

Private Sub CommandButton6_Click()
On Error Resume Next
    ActiveDocument.Unit = cdrMillimeter
    Application.Optimization = False
    ActiveDocument.BeginCommandGroup
    ActiveDocument.ReferencePoint = cdrCenter
    If ActiveDocument Is Nothing Then Exit Sub

    ' 定义变量
    Dim s As ShapeRange
    Dim i As Integer, j As Integer
    Dim frontObjects() As Long ' 正面对象的静态ID数组
    Dim backObjects() As Long ' 背面对象的静态ID数组
    Dim filePath As String ' 文件路径
    Dim objFront As Shape, objBack As Shape
    Dim xFront As Double, xBack As Double
    Dim yFront As Double, yBack As Double
    Dim widthFront As Double, widthBack As Double
    Dim heightFront As Double, heightBack As Double
    Dim isAppendMode As Boolean ' 是否为追加模式
    
    
    ' 设置文件路径
    filePath = Application.GMSManager.GMSPath & "object_ids.txt"

    ' 检查文件是否存在，初始化时清空文件内容
    If Dir(filePath) <> "" Then
        Open filePath For Output As #1
        Close #1
    End If

    ' 初始化
    Set s = ActiveSelectionRange ' 获取选取的对象范围
    If s.Count Mod 2 <> 0 Then
        MsgBox "选中的对象数量必须为偶数！"
        Exit Sub
    End If

    ReDim frontObjects(1 To s.Count / 2) ' 假设前后面对象是1:1配对
    ReDim backObjects(1 To s.Count / 2)

    ' 按照对象的Top和Left排序，确保对象从上到下，从左到右排列
    s.Sort " @shape1.Top * 100 - @shape1.Left > @shape2.Top * 100 - @shape2.Left"

    ' 匹配对象，按顺序判断正反关系
    j = 1 ' 用于匹配的索引
    For i = 1 To s.Count Step 2
        ' 获取成对对象的尺寸和位置
        Set objFront = s(i)
        Set objBack = s(i + 1)

        xFront = objFront.PositionX
        xBack = objBack.PositionX
        yFront = objFront.PositionY
        yBack = objBack.PositionY
        widthFront = objFront.SizeWidth
        widthBack = objBack.SizeWidth
        heightFront = objFront.SizeHeight
        heightBack = objBack.SizeHeight

        If (Abs(widthFront - widthBack) < 0.1) And (Abs(heightFront - heightBack) < 0.1) Then
            ' 判断左右关系
            If xFront < xBack Then
                frontObjects(j) = objFront.StaticID
                backObjects(j) = objBack.StaticID
            Else
                frontObjects(j) = objBack.StaticID
                backObjects(j) = objFront.StaticID
            End If
        Else
            MsgBox "对象 " & i & " 和 " & i + 1 & " 尺寸不匹配，无法判断正反面。"
            Exit Sub
        End If

        j = j + 1 ' 更新匹配索引
    Next i

    Open filePath For Output As #1 ' 以覆盖模式打开文件

    For i = 1 To UBound(frontObjects)
        If frontObjects(i) <> 0 Then
            Print #1, frontObjects(i) & "," & backObjects(i)
        End If
    Next i
    Close #1

    MsgBox "正反面对象标记并保存成功！"

    ActiveDocument.EndCommandGroup
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    Exit Sub
ErrorHandler:
    Application.Optimization = False
    On Error Resume Next
End Sub


Private Sub OptionButton1_Click()
    SaveSetting "JinGuangHe", "ABpinban", "mirrorDirection", "heng"
End Sub


Private Sub OptionButton2_Click()
    SaveSetting "JinGuangHe", "ABpinban", "mirrorDirection", "shu"
End Sub

Private Sub TextBox3_Change()
    Dim gaps As Double
    gaps = Val(TextBox3.Value)
    SaveSetting "JinGuangHe", "bmpToCMYK", "gaps", gaps
End Sub

'// 关闭窗口时保存窗口位置
Private Sub UserForm_QueryClose(Cancel As Integer, CloseMode As Integer)
    saveFormPos True
End Sub

'// 保存窗口位置和加载窗口位置
Sub saveFormPos(bDoSave As Boolean)
  If bDoSave Then
    SaveSetting "JinGuangHe", "windows", "windows_left", Me.Left
    SaveSetting "JinGuangHe", "windows", "windows_top", Me.Top
  End If
End Sub
Private Sub UserForm_Initialize()

    savedgaps = GetSetting("JinGuangHe", "bmpToCMYK", "gaps", 200)
     TextBox3.Value = savedgaps
     If savedgaps = "" Then
        savedgaps = 200
        TextBox3.Value = 200
     End If

    Dim mirrorDirection As String
    mirrorDirection = GetSetting("JinGuangHe", "ABpinban", "mirrorDirection", "heng")
    
    If mirrorDirection = "heng" Then
        OptionButton1.Value = True
        OptionButton2.Value = False
    ElseIf mirrorDirection = "shu" Then
        OptionButton1.Value = False
        OptionButton2.Value = True
    End If

     With Me
    .StartUpPosition = 0
    .Left = Val(GetSetting("JinGuangHe", "windows", "windows_left", 900))
    .Top = Val(GetSetting("JinGuangHe", "windows", "windows_top", 200))
    End With
End Sub
